import 'dart:io';

import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:page/firebase_options.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'src/app.dart';
import 'src/core/localization/app_language.dart';

//! flutter build web --release --web-renderer html
//! GIT_SSH_COMMAND="ssh -i ~/.ssh/id_rsa_ajory" git push -uf origin new_web
//? For Demo
//! GIT_SSH_COMMAND="ssh -i ~/.ssh/id_rsa_ajory" git push -uf origin new_demo_web

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  AppLanguage appLanguage = AppLanguage();

  HttpOverrides.global = MyHttpOverrides();

  String storageLocation =
      kIsWeb ? '' : (await getApplicationDocumentsDirectory()).path;

  Future.wait([
    Firebase.initializeApp(
      options: kIsWeb ? DefaultFirebaseOptions.currentPlatform : null,
    ),
    FastCachedImageConfig.init(
      subDir: storageLocation,
    ),
  ]).then((value) async {
    final languageCode = await appLanguage.fetchLocale();

    runApp(ChangeNotifierProvider(
      create: (_) => appLanguage,
      child: Phoenix(
        child: MyApp(appLanguage: appLanguage, languageCode: languageCode),
      ),
    ));
  });
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}
