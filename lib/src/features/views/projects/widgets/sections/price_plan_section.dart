import 'package:flutter/material.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/projects/widgets/price_plan_table.dart';

class PricePlanSection extends StatelessWidget {
  final VideoModel project;

  const PricePlanSection({
    super.key,
    required this.project,
  });

  @override
  Widget build(BuildContext context) {
    if (project.pricePlan == null ||
        project.paymentMethod == 'cash' ||
        project.pricePlan!.data?.isEmpty == true) {
      return _buildNoPricePlanWidget(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Price Plan Description
        if (_getPricePlanDescription(context)?.isNotEmpty == true) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF27b4a8).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: Color(0xFF27b4a8),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _getPricePlanDescription(context) ?? '',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF27b4a8),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
        ],

        // Price Plan Table
        PricePlanTable(
          data: project.pricePlan!.data ?? [],
        ),
      ],
    );
  }

  Widget _buildNoPricePlanWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.payment_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 12),
            Text(
              AppLocalizations.of(context)
                  .translate('No Payment Plan Available'),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              project.paymentMethod == 'cash'
                  ? AppLocalizations.of(context)
                      .translate('Cash payment method selected')
                  : AppLocalizations.of(context)
                      .translate('Payment plan will be added soon'),
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String? _getPricePlanDescription(BuildContext context) {
    if (project.pricePlan == null) return null;

    return isEnglish(context)
        ? project.pricePlan!.descriptionEn ?? project.pricePlan!.description
        : project.pricePlan!.descriptionAr ?? project.pricePlan!.description;
  }
}
