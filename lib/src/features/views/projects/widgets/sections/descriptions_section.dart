import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/features/models/video_model.dart';

import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../controllers/language_controller.dart';
import '../quill_description_parser.dart';

class DescriptionsSection extends StatefulWidget {
  final VideoModel project;

  const DescriptionsSection({
    super.key,
    required this.project,
  });

  @override
  State<DescriptionsSection> createState() => _DescriptionsSectionState();
}

class _DescriptionsSectionState extends State<DescriptionsSection> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final description = _getDescription(context);

    if (description.isEmpty) {
      return _buildNoDescriptionWidget(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Description content
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Content with truncation
              _buildContentWithTruncation(description),

              // Show more/less button if content is long
              if (_shouldShowExpandButton()) ...[
                const SizedBox(height: 12),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF27b4a8).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _isExpanded
                              ? AppLocalizations.of(context)
                                  .translate('Show Less')
                              : AppLocalizations.of(context)
                                  .translate('Show More'),
                          style: const TextStyle(
                            color: Color(0xFF27b4a8),
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          _isExpanded
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          color: const Color(0xFF27b4a8),
                          size: 18,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),

        // Additional project information
        // if (widget.project.label?.isNotEmpty == true) ...[
        //   const SizedBox(height: 16),
        //   _buildLabelInfo(context),
        // ],

        // Features if available
        // if (widget.project.featureList?.isNotEmpty == true) ...[
        //   const SizedBox(height: 16),
        //   _buildFeaturesSection(context),
        // ],
      ],
    );
  }

  String _getDescription(BuildContext context) {
    // Check if we should use English or Arabic description based on current language
    if (isEnglish(context)) {
      // Try to get English description first, fallback to main description
      return widget.project.description ?? '';
    } else {
      // Try to get Arabic description first, fallback to main description
      return widget.project.description ?? '';
    }
  }

  Widget _buildContentWithTruncation(String description) {
    // Check if description contains JSON (flutter_quill format)
    if (_isQuillContent(description)) {
      return _buildQuillContentWithTruncation(description);
    } else {
      return _buildHtmlContentWithTruncation(description);
    }
  }

  Widget _buildQuillContentWithTruncation(String description) {
    if (!_shouldShowExpandButton()) {
      return QuillDescriptionParser(description: description);
    }

    if (_isExpanded) {
      return QuillDescriptionParser(description: description);
    } else {
      // For collapsed state, create a truncated version
      String truncatedDescription = _truncateQuillContent(description);
      return QuillDescriptionParser(description: truncatedDescription);
    }
  }

  Widget _buildHtmlContentWithTruncation(String description) {
    if (!_shouldShowExpandButton()) {
      return _buildHtmlDescription(description);
    }

    if (_isExpanded) {
      return _buildHtmlDescription(description);
    } else {
      // For collapsed state, show truncated HTML as plain text
      String plainText = description.replaceAll(RegExp(r'<[^>]*>'), '');
      String truncatedText = plainText.length > 200
          ? '${plainText.substring(0, 200)}...'
          : plainText;

      return Text(
        truncatedText,
        style: TextStyle(
          fontSize: 16,
          height: 1.5,
          color: Colors.black87,
          fontFamily: isEnglish(context) ? 'Roboto' : 'Tajawal',
        ),
      );
    }
  }

  String _truncateQuillContent(String quillJson) {
    try {
      // Try to parse and truncate Quill content
      final List<dynamic> quillData = json.decode(quillJson);
      List<dynamic> truncatedData = [];
      int charCount = 0;
      const int maxChars = 200;

      for (var item in quillData) {
        if (item is Map<String, dynamic>) {
          final insert = item['insert'];
          if (insert is String) {
            if (charCount + insert.length <= maxChars) {
              truncatedData.add(item);
              charCount += insert.length;
            } else {
              // Add partial text and break
              String remainingText = insert.substring(0, maxChars - charCount);
              if (remainingText.isNotEmpty) {
                truncatedData.add({
                  'insert': '$remainingText...',
                  'attributes': item['attributes']
                });
              }
              break;
            }
          } else {
            // For non-text content (like images), add as is if we haven't exceeded limit
            if (charCount < maxChars) {
              truncatedData.add(item);
            }
          }
        }
      }

      return json.encode(truncatedData);
    } catch (e) {
      // If parsing fails, return original
      return quillJson;
    }
  }

  bool _shouldShowExpandButton() {
    final description = _getDescription(context);
    if (description.isEmpty) return false;

    // For HTML content, check if it's longer than a certain character count
    if (!_isQuillContent(description)) {
      // Remove HTML tags for length calculation
      final plainText = description.replaceAll(RegExp(r'<[^>]*>'), '').trim();
      return plainText.length > 150; // Show button if more than 150 characters
    }

    // For Quill content, try to extract text and check length
    try {
      if (description.trim().startsWith('[')) {
        // Simple check: if JSON is very long, likely needs truncation
        return description.length > 500;
      }
    } catch (e) {
      // If parsing fails, fall back to string length
      return description.length > 150;
    }

    return false;
  }

  bool _isHtmlContent(String content) {
    // Simple check for HTML tags
    return content.contains('<') && content.contains('>');
  }

  bool _isQuillContent(String content) {
    try {
      // Check if content starts with [ and contains "insert" which is typical for Quill format
      return content.trim().startsWith('[') && content.contains('"insert"');
    } catch (e) {
      return false;
    }
  }

  Widget _buildHtmlDescription(String htmlContent) {
    return HtmlWidget(
      widget.project.description ?? '',
      onLoadingBuilder: (context, element, loadingProgress) => Center(
        child: buildLoadingWidget(),
      ),
    );

    return Html(
      data: htmlContent,
      style: {
        "body": Style(
          fontSize: FontSize(16),
          lineHeight: const LineHeight(1.5),
          color: Colors.black87,
          margin: Margins.zero,
          padding: HtmlPaddings.zero,
        ),
        "p": Style(
          margin: Margins.only(bottom: 12),
        ),
        "h1, h2, h3, h4, h5, h6": Style(
          color: const Color(0xFF27b4a8),
          fontWeight: FontWeight.bold,
          margin: Margins.only(bottom: 8, top: 16),
        ),
        "ul, ol": Style(
          margin: Margins.only(left: 16, bottom: 12),
        ),
        "li": Style(
          margin: Margins.only(bottom: 4),
        ),
      },
      onLinkTap: (url, _, __) {
        // Handle link taps if needed
        print('Link tapped: $url');
      },
    );
  }

  Widget _buildNoDescriptionWidget(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.description_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 12),
            Text(
              AppLocalizations.of(context)
                  .translate('No Description Available'),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              AppLocalizations.of(context)
                  .translate('Description will be added soon'),
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLabelInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.label,
            color: Colors.blue[600],
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            AppLocalizations.of(context).translate('Label'),
            style: TextStyle(
              color: Colors.blue[700],
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.project.label!,
              style: TextStyle(
                color: Colors.blue[800],
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star,
                color: Colors.green[600],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).translate('Features'),
                style: TextStyle(
                  color: Colors.green[700],
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: widget.project.featureList!.map((feature) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.green[100],
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.green[300]!),
                ),
                child: Text(
                  feature,
                  style: TextStyle(
                    color: Colors.green[800],
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
