import 'dart:convert';

import 'package:flutter/material.dart';

import '../../../../core/utils/main_cached_image.dart';

class QuillDescriptionParser extends StatelessWidget {
  final String description;

  const QuillDescriptionParser({
    super.key,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    try {
      // Try to parse as JSON first
      final List<dynamic> quillData = json.decode(description);
      return _buildQuillContent(quillData);
    } catch (e) {
      // If parsing fails, treat as plain text
      return Text(
        description,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
        ),
      );
    }
  }

  Widget _buildQuillContent(List<dynamic> quillData) {
    List<Widget> widgets = [];

    for (var item in quillData) {
      if (item is Map<String, dynamic>) {
        final insert = item['insert'];
        final attributes = item['attributes'] as Map<String, dynamic>?;

        if (insert is String) {
          // Handle text content
          if (insert.trim().isNotEmpty && insert != '\n') {
            widgets.add(_buildTextWidget(insert, attributes));
          }
        } else if (insert is Map<String, dynamic>) {
          // Handle embedded content (like images)
          if (insert.containsKey('image')) {
            widgets.add(_buildImageWidget(insert['image'], attributes));
          }
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  Widget _buildTextWidget(String text, Map<String, dynamic>? attributes) {
    double fontSize = 16;
    FontWeight fontWeight = FontWeight.normal;
    Color color = Colors.black87;
    String fontFamily = 'Poppins';

    if (attributes != null) {
      // Parse font size
      if (attributes['size'] != null) {
        fontSize = double.tryParse(attributes['size'].toString()) ?? 16;
      }

      // Parse color
      if (attributes['color'] != null) {
        String colorStr = attributes['color'].toString();
        if (colorStr.startsWith('#FF')) {
          colorStr = colorStr.substring(3); // Remove #FF prefix
          color = Color(int.parse('FF$colorStr', radix: 16));
        }
      }

      // Parse font family
      if (attributes['font'] != null) {
        fontFamily = attributes['font'].toString();
      }

      // Parse font weight (if bold attribute exists)
      if (attributes['bold'] == true) {
        fontWeight = FontWeight.bold;
      }
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text.trim(),
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          fontFamily: fontFamily,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildImageWidget(String imageUrl, Map<String, dynamic>? attributes) {
    double? width;
    double? height;

    if (attributes != null && attributes['style'] != null) {
      String style = attributes['style'].toString();

      // Parse width from style
      RegExp widthRegex = RegExp(r'width:(\d+)px');
      Match? widthMatch = widthRegex.firstMatch(style);
      if (widthMatch != null) {
        width = double.tryParse(widthMatch.group(1)!);
      }

      // Parse height from style
      RegExp heightRegex = RegExp(r'height:(\d+)px');
      Match? heightMatch = heightRegex.firstMatch(style);
      if (heightMatch != null) {
        height = double.tryParse(heightMatch.group(1)!);
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: MainCachedImage(
          imageUrl,
          width: width,
          height: height,
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
