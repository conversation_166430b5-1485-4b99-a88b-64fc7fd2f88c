import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/services/api.dart';
import 'package:page/src/core/shared_widgets/bottom_navgation_bar.dart';
import 'package:page/src/core/shared_widgets/shared_widgets.dart';
import 'package:page/src/core/utils/print_services.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:page/src/features/controllers/content_controller.dart';
import 'package:page/src/features/controllers/currency_controller.dart';
import 'package:page/src/features/models/content.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/projects/filtered_projects_page.dart';
import 'package:page/src/features/views/projects/widgets/project_card.dart';
import 'package:page/src/features/views/projects/widgets/project_filter.dart';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';

import '../../../core/localization/app_localizations.dart';
import '../account/account.dart';
import '../map/show_on_map/projects_map_page.dart';

String? categoryFilter;

class NewProjectScreen extends StatefulWidget {
  final bool fromBottomNav;

  const NewProjectScreen({Key? key, this.fromBottomNav = false})
      : super(key: key);

  @override
  _NewProjectWidget createState() => _NewProjectWidget();
}

class _NewProjectWidget extends State<NewProjectScreen> {
  List<VideoModel> results = [];
  List<VideoModel> featuredvideo = [];
  int pagenumber = 1;
  int code2 = 0;
  String msg2 = 'loading';
  bool isLoading = false;

  final ContentController contentController = ContentController();
  final CurrencyController currencyController = CurrencyController();
  final AuthController authController = AuthController();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    initializeData();
  }

  Future<void> initializeData() async {
    await Future.wait([
      if (ContentController.locations.isEmpty) contentController.getlocations(),
      if (ContentController.propertyStatuses.isEmpty)
        contentController.getPropertyStatuses(),
      contentController.gettypes(AppConstants.projectsId.toString()),
      authController.isloggedin(),
      currencyController.getcuurentcurrency(context),
    ]);

    await getProjects(pagenumber, "");
  }

  Future<void> getProjects(int page, String key) async {
    setState(() {
      isLoading = true;
    });

    await Api.getmainCategory(
      page,
      10,
      key,
      AppConstants.projectsId.toString(),
    ).then((value) {
      setState(() {
        code2 = value.code;
        msg2 = value.error;
        isLoading = false;
        results.addAll(value.category);
        if (searchController.text.isEmpty) {
          featuredvideo.addAll(value.featuredvideo);
        }
      });
    });
  }

  Future<void> filterProjects() async {
    results.clear();
    setState(() {
      isLoading = true;
    });

    await Api.filterProjectsCategory(
      projectLocationValue,
      projectPropertyStatusValue,
      projectPaymentMethodValue,
      null,
    ).then((value) {
      if (value != null) {
        setState(() {
          code2 = value.code;
          msg2 = value.error;
          isLoading = false;
          results.addAll(value.category);
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    });
  }

  void clearResultsAndFeaturedVideos() {
    setState(() {
      results.clear();
      if (searchController.text.isEmpty) {
        featuredvideo.clear();
      }
    });
  }

  void onSearchSubmitted(String text) async {
    clearResultsAndFeaturedVideos();
    if (text.isEmpty) {
      await getProjects(1, "");
    } else {
      pagenumber = 1;
      await getProjects(1, text);
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const Account()),
          );

          return Future.value(false);
        },
        child: SafeArea(
            child: Scaffold(
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
          floatingActionButton: ElevatedButton(
            onPressed: () {
              Navigator.of(context).push(MaterialPageRoute(
                builder: (context) => const ProjectsMapPage(),
              ));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(100),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.location_on,
                  color: Colors.white,
                  size: 22,
                ),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).translate('Show On Map'),
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: CustomBottomNavgationBar(3),
          appBar: AppBar(
            automaticallyImplyLeading: widget.fromBottomNav ? false : true,
            backgroundColor: const Color(0xFF27b4a8),
            centerTitle: true,
            title: Text(
              AppLocalizations.of(context).translate('Projects'),
              style: TextStyle(
                fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
              ),
            ),
            // actions: [
            //   IconButton(
            //     onPressed: () {
            //       projectFilter(
            //         context,
            //         onApply: filterProjects,
            //         contentController: contentController,
            //       );
            //     },
            //     icon: SizedBox(
            //       width: 20,
            //       height: 20.0,
            //       child: SvgPicture.asset(
            //         'assets/filter.svg',
            //         semanticsLabel: 'Filter',
            //         fit: BoxFit.cover,
            //       ),
            //     ),
            //   ),
            // ],
          ),
          body: ListView(
            children: [
              // Search bar
              Container(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: AppLocalizations.of(context).translate('Search'),
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey[200],
                  ),
                  onSubmitted: onSearchSubmitted,
                ),
              ),
              // Project types horizontal list
              SizedBox(
                height: 110,
                child: ListView(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  scrollDirection: Axis.horizontal,
                  children: allTypes
                      .where((element) =>
                          element.categoryIds.contains(AppConstants.projectsId))
                      .toList()
                      .map(
                        (e) => _buildTypeCard(
                          context,
                          type: e,
                        ),
                      )
                      .toList(),
                ),
              ),
              // Latest Projects section
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('Latest Projects'),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              // Projects list
              isLoading
                  ? buildLoadingWidget()
                  : code2 == 1
                      ? results.isNotEmpty
                          ? ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              itemCount: results.length,
                              itemBuilder: (context, index) {
                                return ProjectCard(
                                  project: results[index],
                                );
                              },
                            )
                          : nodatafound(AppLocalizations.of(context)
                              .translate('No Projects to show'))
                      : buildLoadingWidget(),
            ],
          ),
        )));
  }

  Widget _buildTypeCard(BuildContext context, {required Types type}) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => FilteredProjectsPage(type: type),
          ),
        );
      },
      child: Container(
        width: 80,
        margin: const EdgeInsets.symmetric(horizontal: 5),
        child: Column(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(30),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: type.image?.isNotEmpty == true
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(30),
                      child: Image.network(
                        type.image!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(
                            Icons.business,
                            color: Colors.grey,
                            size: 30,
                          );
                        },
                      ),
                    )
                  : const Icon(
                      Icons.business,
                      color: Colors.grey,
                      size: 30,
                    ),
            ),
            const SizedBox(height: 8),
            Text(
              type.name ?? '',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
