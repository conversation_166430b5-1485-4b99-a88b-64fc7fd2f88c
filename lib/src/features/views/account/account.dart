import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/features/views/account/widgets/account_information/account_information.dart';
import 'package:page/src/features/views/account/widgets/account_request.dart';
import 'package:page/src/features/views/account/widgets/terms/terms_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/response/category_response.dart';
import '../../../core/response/profile_response.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/bottom_navgation_bar.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../bloc/profile_bloc.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/currency_controller.dart';
import '../../models/model_select.dart';
import '../home/<USER>';
import '../notificatons/notifications.dart';
import '../splash_screen/view/second_splash.dart';
import 'widgets/change_currency_or_lang_bottom_sheet.dart';
import 'widgets/favorite_list.dart';
import 'widgets/terms/contact_us.dart';

ConfigurationResponse? appConfiguration;

class Account extends StatefulWidget {
  const Account({super.key});

  @override
  _Account createState() => _Account();
}

class _Account extends State<Account> {
  TextEditingController fullnameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController messageController = TextEditingController();
  List<ModelSelect> langList = [];
  List<ModelSelect> currencyList = [];
  String? youtube, instagram;
  String lang = 'en';
  String currency = 'AED';
  CurrencyController currencyController = CurrencyController();
  String? policy;
  int page = 1;
  bool switchOn2 = false;
  bool islogin = false;
  bool issel = true;
  bool issel2 = false;

  void getconfiguration() async {
    await Api.getconfiguration().then((value) {
      value != null
          ? setState(() {
              log('value.resultsasdasd ${value.results}');
              youtube = value.results[AppConstants.youtube];
              instagram = value.results[AppConstants.instagram];
              policy = value.results[AppConstants.policy];
            })
          // ignore: unnecessary_statements
          : null;
    });
  }

  getdefaultlangage() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var languagecode = prefs.getString('language_code');
    print("ttttttt");
    print(languagecode);
    if (languagecode == "ar") {
      setState(() {
        lang = 'ar';
      });
    } else {
      setState(() {
        lang = 'en';
      });
    }
    langList = [
      ModelSelect('English', lang == 'en' ? true : false),
      ModelSelect('العربية', lang == 'ar' ? true : false)
    ];
  }

  getcurrentcurrency() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var currentcurrency = prefs.getString('currency');
    print("ttttttt");
    print(currentcurrency);
    if (currentcurrency == "USD") {
      setState(() {
        currency = 'USD';
      });
    } else {
      if (currentcurrency == "EUR") {
        setState(() {
          currency = 'EUR';
        });
      }
    }
    currencyList = [
      ModelSelect('AED', currency == 'AED' ? true : false),
      ModelSelect('USD', currency == 'USD' ? true : false),
      ModelSelect('EUR', currency == 'EUR' ? true : false)
    ];
  }

  bool isNotificationTurnedOn = false;

  final authController = AuthController();

  @override
  void initState() {
    super.initState();
    // getpolicy();
    getconfiguration();
    isloggedin();
    getdefaultlangage();
    print("lang");
    print(lang);

    authController.isloggedin().then((value) {
      if (authController.isLogged) {
        bloc2.getProfile().then((value) {
          setState(() {
            isNotificationTurnedOn = value.results['fcmtoken'] != null;
          });
        });
      }
    });

    currencyController.getcuurentcurrency(context);
    getcurrentcurrency();
  }

  isloggedin() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    var islogged = prefs.getBool('is_logged');

    if (islogged == true) {
      print("DFfehehetet");

      setState(() {
        islogin = true;
      });
      bloc2.getProfile();
      bloc2.getfavouritecategory(page, 20);
      // bloc2.getfavouriteuserVibes(page, 20);
    } else {
      setState(() {
        islogin = false;
      });
    }
    print(islogin);
  }

  Widget getprofile(ProfileResponse data) {
    return Row(
      children: [
        Container(
            height: 50,
            width: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              color: const Color(0xffE4E4E4),
            ),
            child: Center(
                child: data.results['fullname'].length > 1
                    ? data.results['fullname'] != null
                        ? Text(
                            data.results['fullname'].substring(0, 2),
                            style: const TextStyle(color: Color(0xffB7B7B7)),
                          )
                        : Container()
                    : Text(
                        data.results['fullname'],
                        style: const TextStyle(color: Color(0xffB7B7B7)),
                      ))),
        const SizedBox(
          width: 20,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            data.results['fullname'] != null
                ? Text(data.results['fullname'],
                    style: const TextStyle(color: Colors.white, fontSize: 16))
                : Container(),
            data.results['email'] != null
                ? Text(data.results['email'],
                    style: const TextStyle(color: Colors.white, fontSize: 12))
                : Container(),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () {
          Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const Home()),
              (Route<dynamic> route) => false);

          return Future.value(false);
        },
        child: SafeArea(
            child: Scaffold(
          body: SingleChildScrollView(
              child: Column(children: [
            Stack(
              children: [
                InkWell(
                    onTap: () {
                      islogin
                          ? Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  Accountinformation()))
                          // ignore: unnecessary_statements
                          : "";
                    },
                    child: Container(
                        padding: const EdgeInsets.only(bottom: 80),
                        child: Container(
                            width: MediaQuery.of(context).size.width,
                            height: MediaQuery.of(context).size.height / 4,
                            color: const Color(0xFF27b4a8),
                            child: Container(
                                padding:
                                    const EdgeInsets.only(left: 20, right: 20),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    islogin
                                        ? StreamBuilder<ProfileResponse>(
                                            stream: bloc2.subject.stream,
                                            builder: (context,
                                                AsyncSnapshot<ProfileResponse>
                                                    snapshot) {
                                              if (snapshot.hasData) {
                                                if (snapshot
                                                    .data!.error.isNotEmpty) {
                                                  return buildErrorWidget(
                                                      snapshot.data!.error);
                                                }
                                                return Container(
                                                    child: getprofile(
                                                        snapshot.data!));
                                              } else if (snapshot.hasError) {
                                                return buildErrorWidget(
                                                    snapshot.error.toString());
                                              } else {
                                                return Container(
                                                    // height: MediaQuery.of(context)
                                                    //     .size
                                                    //     .height,
                                                    child:
                                                        buildLoadingWidget());
                                              }
                                            },
                                          )
                                        : Container(
                                            padding: const EdgeInsets.only(
                                                bottom: 20),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  AppLocalizations.of(context)
                                                      .translate(
                                                          'LoginRegister'),
                                                  style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 18),
                                                ),
                                                Text(
                                                    AppLocalizations.of(context)
                                                        .translate('Getaccess'),
                                                    style: const TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 12)),
                                              ],
                                            )),
                                  ],
                                ))))),
                AccountTopSection(
                  islogin: islogin,
                  onTapFav: () {
                    favouirtelist(context,
                        issel: issel,
                        issel2: issel2,
                        currencyController: currencyController,
                        page: page);
                  },
                ),
              ],
            ),
            20.verticalSpace,
            Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: Container(
                  padding: const EdgeInsets.all(15),
                  color: Colors.white,
                  child: Column(
                    children: [
                      islogin
                          ? InkWell(
                              onTap: () {
                                Navigator.of(context).pushReplacement(
                                    MaterialPageRoute(
                                        builder: (BuildContext context) =>
                                            Accountinformation()));
                              },
                              child: Row(
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        AppLocalizations.of(context)
                                            .translate('Accountinformation'),
                                        style: const TextStyle(
                                            color: Color(
                                              0xff191C1F,
                                            ),
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12),
                                      ),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                      Text(
                                        AppLocalizations.of(context).translate(
                                            'Editprofilechangepassword'),
                                        style: const TextStyle(
                                            color: Color(0xff8B959E),
                                            fontSize: 12),
                                      )
                                    ],
                                  ),
                                  const Spacer(),
                                  lang == 'en'
                                      ? const Icon(Icons.keyboard_arrow_right,
                                          color: Color(0xff191C1F))
                                      : const Icon(Icons.keyboard_arrow_left,
                                          color: Color(0xff191C1F))
                                ],
                              ))
                          : Container(),
                      const SizedBox(
                        height: 10,
                      ),
                      InkWell(
                        onTap: () async {
                          showModalBottomSheet(
                              elevation: 10,
                              context: context,
                              isScrollControlled: false,
                              backgroundColor: Colors.transparent,
                              builder: (ctx) => ChangeCurrencyOrLangBottomSheet(
                                  langList,
                                  AppLocalizations.of(context)
                                      .translate('Language'),
                                  'Language'));
                        },
                        child: Row(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.of(context)
                                      .translate('Language'),
                                  style: const TextStyle(
                                      color: Color(
                                        0xff191C1F,
                                      ),
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Text(
                                  lang == 'en' ? 'English' : 'العربية',
                                  style: const TextStyle(
                                      color: Color(0xff8B959E), fontSize: 12),
                                )
                              ],
                            ),
                            const Spacer(),
                            lang == 'en'
                                ? const Icon(Icons.keyboard_arrow_right,
                                    color: Color(0xff191C1F))
                                : const Icon(Icons.keyboard_arrow_left,
                                    color: Color(0xff191C1F))
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      InkWell(
                          onTap: () {
                            showModalBottomSheet(
                                elevation: 10,
                                context: context,
                                isScrollControlled: false,
                                backgroundColor: Colors.transparent,
                                builder: (ctx) =>
                                    ChangeCurrencyOrLangBottomSheet(
                                        currencyList,
                                        AppLocalizations.of(context)
                                            .translate('Currency'),
                                        'Currency'));
                          },
                          child: Row(
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  InkWell(
                                    onTap: () {
                                      showModalBottomSheet(
                                          elevation: 10,
                                          context: context,
                                          isScrollControlled: false,
                                          backgroundColor: Colors.transparent,
                                          builder: (ctx) =>
                                              ChangeCurrencyOrLangBottomSheet(
                                                  currencyList,
                                                  AppLocalizations.of(context)
                                                      .translate('Currency'),
                                                  'Currency'));
                                    },
                                    child: Text(
                                      AppLocalizations.of(context)
                                          .translate('Currency'),
                                      style: const TextStyle(
                                          color: Color(
                                            0xff191C1F,
                                          ),
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12),
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Text(
                                    currency,
                                    style: const TextStyle(
                                        color: Color(0xff8B959E), fontSize: 12),
                                  )
                                ],
                              ),
                              const Spacer(),
                              lang == 'en'
                                  ? const Icon(Icons.keyboard_arrow_right,
                                      color: Color(0xff191C1F))
                                  : const Icon(Icons.keyboard_arrow_left,
                                      color: Color(0xff191C1F))
                            ],
                          )),
                      const SizedBox(
                        height: 20,
                      ),
                      InkWell(
                          onTap: () {
                            islogin
                                ? Navigator.of(context).push(MaterialPageRoute(
                                    builder: (BuildContext context) =>
                                        Notifications(
                                          isNotificationTurnedOn:
                                              isNotificationTurnedOn,
                                        )))
                                : snackbar(AppLocalizations.of(context)
                                    .translate('Please login first'));
                          },
                          child: Row(
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    AppLocalizations.of(context)
                                        .translate('Notifications'),
                                    style: const TextStyle(
                                        color: Color(
                                          0xff191C1F,
                                        ),
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12),
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Text(
                                    isNotificationTurnedOn
                                        ? AppLocalizations.of(context)
                                            .translate('TurnedOn')
                                        : AppLocalizations.of(context)
                                            .translate('TurnedOff'),
                                    style: const TextStyle(
                                        color: Color(0xff8B959E), fontSize: 12),
                                  )
                                ],
                              ),
                              const Spacer(),
                              lang == 'en'
                                  ? const Icon(Icons.keyboard_arrow_right,
                                      color: Color(0xff191C1F))
                                  : const Icon(Icons.keyboard_arrow_left,
                                      color: Color(0xff191C1F))
                            ],
                          )),
                    ],
                  ),
                )),
            20.verticalSpace,
            TermsWidget(
              data: policy,
              onTap: () {
                contactus(context,
                    fullnameController: fullnameController,
                    emailController: emailController,
                    messageController: messageController);
              },
            ),
            const SizedBox(
              height: 20,
            ),
            islogin
                ? InkWell(
                    onTap: () async {
                      SharedPreferences prefs =
                          await SharedPreferences.getInstance();
                      prefs.setBool('is_logged', false);
                      prefs.setString('token', "");
                      Navigator.of(context).pushAndRemoveUntil(
                          MaterialPageRoute(
                              builder: (context) => SecondSplash()),
                          (Route<dynamic> route) => false);
                    },
                    child: Container(
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          color: Colors.white,
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    AppLocalizations.of(context)
                                        .translate('logout'),
                                    style: const TextStyle(
                                        color: Color(
                                          0xffD8B77F,
                                        ),
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12),
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  const Spacer(),
                                  lang == 'en'
                                      ? const Icon(Icons.keyboard_arrow_right,
                                          color: Color(0xffD8B77F))
                                      : const Icon(Icons.keyboard_arrow_left,
                                          color: Color(0xffD8B77F))
                                ],
                              ),
                            ],
                          ),
                        )))
                : Container(),
            const SizedBox(
              height: 20,
            ),
            islogin
                ? InkWell(
                    onTap: () async {
                      DeleteAccount();
                    },
                    child: Container(
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          color: Colors.white,
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    AppLocalizations.of(context)
                                        .translate('Delete Account'),
                                    style: const TextStyle(
                                        color: Color.fromARGB(255, 235, 2, 2),
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12),
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  const Spacer(),
                                  lang == 'en'
                                      ? const Icon(Icons.keyboard_arrow_right,
                                          color: Color(0xffD8B77F))
                                      : const Icon(Icons.keyboard_arrow_left,
                                          color: Color(0xffD8B77F))
                                ],
                              ),
                            ],
                          ),
                        )))
                : Container(),
            const SizedBox(
              height: 20,
            ),
            Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: Container(
                  padding: const EdgeInsets.all(15),
                  color: Colors.white,
                  child: Column(
                    children: [
                      InkWell(
                          onTap: () {
                            // ignore: unnecessary_statements
                            instagram != null
                                ? launchUrlString(instagram!)
                                // ignore: unnecessary_statements
                                : null;
                          },
                          child: Row(
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  InkWell(
                                      onTap: () {
                                        instagram != null
                                            ? launchUrlString(instagram!)
                                            // ignore: unnecessary_statements
                                            : null;
                                      },
                                      child: Row(
                                        children: [
                                          Image.asset(
                                            'assets/instagram.jpg',
                                            height: 20,
                                            width: 20,
                                          ),
                                          const SizedBox(
                                            width: 5,
                                          ),
                                          const Text(
                                            'Aqar Dubai',
                                            // 'Dubai_Page',
                                            style: TextStyle(
                                                color: Color(
                                                  0xff191C1F,
                                                ),
                                                fontWeight: FontWeight.bold,
                                                fontSize: 12),
                                          )
                                        ],
                                      )),
                                ],
                              ),
                              const Spacer(),
                              lang == 'en'
                                  ? const Icon(Icons.keyboard_arrow_right,
                                      color: Color(0xff191C1F))
                                  : const Icon(Icons.keyboard_arrow_left,
                                      color: Color(0xff191C1F))
                            ],
                          )),
                      const SizedBox(
                        height: 20,
                      ),
                      InkWell(
                        onTap: () {
                          // ignore: unnecessary_statements
                          youtube != null ? launchUrlString(youtube!) : null;
                        },
                        child: Row(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Image.asset(
                                      'assets/download.png',
                                      height: 20,
                                      width: 20,
                                    ),
                                    const SizedBox(
                                      width: 5,
                                    ),
                                    const Text(
                                      'Aqar Dubai',
                                      style: TextStyle(
                                          color: Color(
                                            0xff191C1F,
                                          ),
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12),
                                    )
                                  ],
                                ),
                              ],
                            ),
                            const Spacer(),
                            lang == 'en'
                                ? const Icon(Icons.keyboard_arrow_right,
                                    color: Color(0xff191C1F))
                                : const Icon(Icons.keyboard_arrow_left,
                                    color: Color(0xff191C1F))
                          ],
                        ),
                      ),
                    ],
                  ),
                )),
            const SizedBox(
              height: 20,
            )
          ])),
          bottomNavigationBar: CustomBottomNavgationBar(4),
        )));
  }

  void DeleteAccount() {
    Widget okButton = TextButton(
      child: const Text("OK"),
      onPressed: () async {
        SharedPreferences prefs = (await SharedPreferences.getInstance());
        prefs.setBool('is_logged', false);
        prefs.setString('token', "");
        Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const SecondSplash()),
            (Route<dynamic> route) => false);
        bloc2.deleteAccount();
      },
    );

    Widget noButton = TextButton(
      child: const Text("No"),
      onPressed: () {
        Navigator.pop(context, false);
      },
    );

    AlertDialog alert = AlertDialog(
      title: const Text("Are you sure to delete your account"),
      actions: [
        okButton,
        noButton,
      ],
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }
}
