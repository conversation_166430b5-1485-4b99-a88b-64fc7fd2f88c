import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppLanguage extends ChangeNotifier {
  Locale _appLocale = const Locale('en');

  Locale get appLocal => _appLocale;
  Future<String> fetchLocale() async {
    var prefs = await SharedPreferences.getInstance();
    if (prefs.getString('language_code') == null) {
      _appLocale = const Locale('en');
      return _appLocale.languageCode;
    }
    _appLocale = Locale(prefs.getString('language_code') ?? 'en');
    return _appLocale.languageCode;
  }

  void changeLanguage(Locale type) async {
    print(type);
    var prefs = await SharedPreferences.getInstance();
    if (type == const Locale("ar")) {
      _appLocale = const Locale("ar");
      await prefs.setString('language_code', 'ar');
      await prefs.setString('countryCode', '');
    } else {
      _appLocale = const Locale("en");
      await prefs.setString('language_code', 'en');
      await prefs.setString('countryCode', 'US');
    }
    Get.updateLocale(_appLocale);
    prefs.getString('language_code');
    notifyListeners();
  }

  static void getDefaultLanguage() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    var languageCode = _prefs.getString('language_code');
    if (languageCode == null) {
      _prefs.setString("language_code", "en");
    }
  }

  // cancel dispose
  // @override
  // void dispose() {
  //   super.dispose();
  // }
}
