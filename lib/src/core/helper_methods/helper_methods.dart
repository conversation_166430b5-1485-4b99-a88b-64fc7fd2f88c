import 'package:ip_country_lookup/ip_country_lookup.dart';
import 'package:ip_country_lookup/models/ip_country_data_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HelperMethods {
  static Future<void> getDefaultCurrency() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    var currency = _prefs.getString('currency');

    if (currency == null) {
      final currencyPerCountry = await _getCurrencyBasedOnCountryCode();

      _prefs.setString("currency", currencyPerCountry);
    }
  }

  static Future<String> _getCurrentCountry() async {
    IpCountryData countryData = await IpCountryLookup().getIpLocationData();

    return countryData.country_name ?? '';
  }

  static Future<String> _getCurrencyBasedOnCountryCode() {
    // Only AED || USD || EUR
    //? Check if country in europe -> EUR, or in countries deal with USD -> USD, else -> AED
    return _getCurrentCountry()
        .timeout(const Duration(seconds: 15),
            onTimeout: () => 'AED')
        .then((country) {
      if (country == 'United States' ||
          country == 'Ecuador' ||
          country == 'El Salvador' ||
          country == 'Zimbabwe' ||
          country == 'Palau' ||
          country == 'Marshall Islands' ||
          country == 'Panama' ||
          country == 'The British Virgin Islands' ||
          country == 'Turks and Caicos' ||
          country == 'Timor and Leste' ||
          country == 'Micronesia' ||
          country == 'Bonaire') {
        return 'USD';
      } else if (country == 'Germany' ||
          country == 'France' ||
          country == 'Italy' ||
          country == 'Spain' ||
          country == 'Netherlands' ||
          country == 'Belgium' ||
          country == 'Austria' ||
          country == 'Portugal' ||
          country == 'Greece' ||
          country == 'Sweden' ||
          country == 'Hungary' ||
          country == 'Romania' ||
          country == 'Czech Republic' ||
          country == 'Ireland' ||
          country == 'Denmark' ||
          country == 'Finland' ||
          country == 'Bulgaria' ||
          country == 'Croatia' ||
          country == 'Slovakia' ||
          country == 'Lithuania' ||
          country == 'Slovenia' ||
          country == 'Latvia' ||
          country == 'Estonia' ||
          country == 'Cyprus' ||
          country == 'Luxembourg' ||
          country == 'Malta') {
        return 'EUR';
      } else {
        return 'AED';
      }
    });
  }
}
